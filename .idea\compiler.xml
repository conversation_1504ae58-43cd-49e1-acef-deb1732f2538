<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="tpFoyer-17" />
      </profile>
      <profile name="Annotation profile for smartconseil-back" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/unknown/lombok-unknown.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/unknown/lombok-unknown.jar" />
        </processorPath>
        <module name="microserviceConseil" />
        <module name="microservicePlanification" />
      </profile>
      <profile name="Annotation profile for microserviceRectification" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar" />
        </processorPath>
        <module name="microserviceRectification" />
      </profile>
      <profile name="Annotation profile for microserviceRapport" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar" />
        </processorPath>
        <module name="microserviceRapport" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="SmartConseil-Back" target="1.5" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="microserviceConseil" options="-parameters" />
      <module name="microservicePlanification" options="-parameters" />
      <module name="microserviceRapport" options="-parameters" />
      <module name="microserviceRectification" options="-parameters" />
      <module name="tpFoyer-17" options="-parameters" />
    </option>
  </component>
</project>