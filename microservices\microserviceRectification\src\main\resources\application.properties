spring.application.name=microserviceRectification

spring.datasource.url=********************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=
#spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

spring.jpa.show-sql=true
spring.jpa.hibernate.ddl-auto=update
server.port=8089
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect


