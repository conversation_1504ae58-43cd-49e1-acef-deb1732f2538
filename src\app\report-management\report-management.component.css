.badge-warning {
  background-color: #ffc107;
  color: #000;
}

.badge-success {
  background-color: #198754;
  color: #fff;
}

.badge-secondary {
  background-color: #6c757d;
  color: #fff;
}

.nav-tabs .nav-link {
  cursor: pointer;
}

.nav-tabs .nav-link.active {
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}

.card {
  transition: transform 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-body {
  flex: 1;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.card-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 12px 15px;
  display: block !important;
  visibility: visible !important;
  min-height: 50px;
}

.btn-group .btn {
  flex: 1;
}

.btn-outline-primary {
  color: #0d6efd;
  border-color: #0d6efd;
}

.btn-outline-primary:hover {
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: #fff;
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #fff;
}

.btn-outline-success {
  color: #198754;
  border-color: #198754;
}

.btn-outline-success:hover {
  background-color: #198754;
  border-color: #198754;
  color: #fff;
}

.btn-outline-danger {
  color: #dc3545;
  border-color: #dc3545;
}

.btn-outline-danger:hover {
  background-color: #dc3545;
  border-color: #dc3545;
  color: #fff;
}

.modal.show {
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-lg {
  max-width: 800px;
}

.form-label {
  font-weight: 500;
}

.form-control:focus,
.form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.alert {
  border-radius: 0.375rem;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.fa-3x {
  font-size: 3em;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bg-light {
  background-color: #f8f9fa !important;
}

.border {
  border: 1px solid #dee2e6 !important;
}

.rounded {
  border-radius: 0.375rem !important;
}

/* Custom scrollbar for textarea */
textarea.form-control {
  resize: vertical;
  min-height: 120px;
}

/* Card title truncation */
.card-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Badge positioning */
.nav-link .badge {
  font-size: 0.75em;
}

/* Button group spacing and visibility */
.btn-group {
  display: flex !important;
  width: 100% !important;
}

.btn-group .btn {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  flex: 1;
  min-height: 32px;
  padding: 6px 8px;
  font-size: 14px;
  line-height: 1.2;
}

.btn-group .btn:not(:last-child) {
  border-right: none;
}

.btn-group .btn:not(:first-child) {
  border-left: none;
}

.btn-group .btn i {
  font-size: 12px;
  display: inline-block;
}

/* Modal content spacing */
.modal-body hr {
  margin: 1.5rem 0;
}

/* Pre-wrap for content display */
.modal-body [style*="white-space: pre-wrap"] {
  font-family: inherit;
  line-height: 1.5;
  max-height: 300px;
  overflow-y: auto;
}

/* Ensure buttons are always visible */
.card-footer .btn {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 1 !important;
}

/* Button hover effects */
.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  min-width: 32px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Disabled button styling */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Icon spacing in buttons */
.btn i {
  margin: 0;
  line-height: 1;
}

/* Button text spacing */
.btn .ms-1 {
  margin-left: 4px !important;
}

/* Responsive button text */
@media (max-width: 768px) {
  .btn-group .btn {
    min-width: 40px;
    padding: 6px 4px;
  }

  .btn-group .btn span {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .btn-group .btn {
    min-width: 80px;
    padding: 6px 12px;
  }
}

/* Force button visibility */
.card-footer .btn-group .btn {
  background-color: transparent !important;
  border: 1px solid !important;
  color: inherit !important;
  text-decoration: none !important;
  cursor: pointer !important;
}

/* Ensure proper button states */
.btn-outline-primary:not(:disabled) {
  color: #0d6efd !important;
  border-color: #0d6efd !important;
}

.btn-outline-secondary:not(:disabled) {
  color: #6c757d !important;
  border-color: #6c757d !important;
}

.btn-outline-success:not(:disabled) {
  color: #198754 !important;
  border-color: #198754 !important;
}

.btn-outline-danger:not(:disabled) {
  color: #dc3545 !important;
  border-color: #dc3545 !important;
}
