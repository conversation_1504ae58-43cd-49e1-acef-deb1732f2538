body {
  font-family: 'Inter', sans-serif;
  background: #f5f7fa;
  margin: 0;
  padding: 0;
}

.login-container {
  max-width: 580px;
  margin: 100px auto;
  padding: 20px;
  margin-top: 90px;
  
}
.password-box {
  margin-top: 20px;
}

.password-box input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
}


.login-header {
  position: absolute;
  top: 20px; /* Ajuste selon ton besoin */
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  margin-bottom: 0;
}

.icon-shield {
  font-size: 48px;
  margin-top: -20px;

}

.login-header h1 {
  margin: -10px 0 0 0; /* Remonte de 10px */
  font-size: 32px;
}




.login-box {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 35px 40px;
}

.login-box h2 {
  margin-top: 0;
  font-size: 22ppx;
}

.subtitle {
  font-size: 15px;
  color: #666;
  margin-bottom: 25px;
}

label {
  display: block;
  margin-top: 20px;
  font-weight: 600;
  font-size: 15px;
  color: black;
}

select, input {
  width: 100%;
  padding: 14px 16px;
  margin-top: 8px;
  border: 1px solid #ccc;
  border-radius: 10px;
  font-size: 15px;
}

.input-group {
  position: relative;
}

.input-group .icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #888;
  font-size: 16px;
}

.input-group input {
  padding-left: 36px;
}

.auth-methods {
  display: flex;
  justify-content: space-between;
  margin: 25px 0 20px;
  gap: 10px;
}

.auth-btn {
  flex: 1;
  padding: 12px 0;
  background: #f0f0f0;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  font-size: 15px;
  transition: background 0.2s;
}

.auth-btn.active {
  background: #0d6efd;
  color: white;
}

.face-box {
  margin: 10px 0 30px;
}

.face-frame {
  border: 2px dashed #ccc;
  border-radius: 14px;
  text-align: center;
  padding: 25px 15px;
}

.camera-icon {
  font-size: 28px;
  margin-bottom: 10px;
}

.start-recognition {
  margin-top: 12px;
  padding: 10px 18px;
  background: #0d6efd;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
}

.connect-btn {
  width: 100%;
  background: #0d6efd;
  color: white;
  padding: 14px;
  border: none;
  border-radius: 10px;
  font-weight: bold;
  font-size: 17px;
  cursor: pointer;
  margin-top: 20px;
}

.forgot-link {
  text-align: center;
  margin-top: 18px;
}

.forgot-link a {
  color: #0d6efd;
  text-decoration: none;
  font-size: 15px;
}
.auth-title {
  font-size: 18px;         /* Taille du texte */
  font-weight: 600;        /* Poids de la police */
  margin: 30px 0 10px 0;   /* Marge : haut, droite, bas, gauche */
  color: #333;             /* (Optionnel) Couleur du texte */
}
.error-message {
  color: red;
  font-size: 0.85em;
  margin-top: 5px;
  margin-bottom: 10px;
}

.input-group input.ng-invalid.ng-touched {
  border-color: red;
}
.face-frame {
  border: 3px solid #007bff;
  padding: 16px;
  text-align: center;
  border-radius: 10px;
  width: 280px; /* smaller width */
  margin: auto;
  background-color: #f0f0f0;
}

.video-frame {
  width: 240px;  /* reduced from 100% */
  height: 180px;
  border: 2px dashed #007bff;
  margin-bottom: 10px;
  border-radius: 10px;
  object-fit: cover;
}
.face-frame video {
  width: 400px !important;     /* smaller width */
  height: auto !important;     /* keep aspect ratio */
  max-height: 150px;           /* limit max height */
  border-radius: 8px;          /* optional rounded corners */
  object-fit: cover;           /* maintain aspect ratio and crop */
  display: block;
  margin-bottom: 10px;
}
.face-box {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.face-frame {
  background: #fafafa;
  border-radius: 12px;
  padding: 20px;
  max-width: 700px;
  width: 100%;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  text-align: center;
  font-family: Arial, sans-serif;
}

.my-webcam video {
  width: 600px !important;
  height: 400px !important;
  border-radius: 10px;
  object-fit: cover;
  display: block;
  margin: 100px auto 15px auto;
  box-shadow: 0 0 8px rgba(0,0,0,0.15);
  margin-bottom: 8px;
}

button {
  background-color: #3f51b5;
  border: none;
  color: rgb(0, 0, 0);
  padding: 10px 18px;
  font-size: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

button:hover {
  background-color: #495ee5;
    color: rgb(255, 255, 255);
}

p {
  margin: 10px 0;
  color: #000000;
  font-weight: 500;
}
