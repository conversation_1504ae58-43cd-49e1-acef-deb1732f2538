<div class="page-wrapper" id="main-wrapper" data-layout="vertical" data-navbarbg="skin6" data-sidebartype="full"
  data-sidebar-position="fixed" data-header-position="fixed">
  
  <!-- Sidebar Start -->
  <aside class="left-sidebar">
    <!-- Sidebar scroll-->
    <div>
      <div class="login-header">
        <img src="assets/images/logos/esprit.png" alt="" style="width: 200px; height: auto; display: block; margin-left: 0px;" />
    
      </div>
      <div class="brand-logo d-flex align-items-center justify-content-between">
        <a href="./index.html" class="text-nowrap logo-img">
          <img src="../assets/images/logos/dark-logo.svg" width="180" alt="" />
        </a>
        <div class="close-btn d-xl-none d-block sidebartoggler cursor-pointer" id="sidebarCollapse">
          <i class="ti ti-x fs-8"></i>
        </div>
      </div>
      
      <!-- Sidebar navigation-->
      <nav class="sidebar-nav scroll-sidebar" data-simplebar="">
        <ul id="sidebarnav">
          <li class="nav-small-cap">
            <span class="hide-menu">Tableau de Bord Rapporteur</span>
          </li>

          <li class="sidebar-item">
            <a class="sidebar-link" href="#" aria-expanded="false">
              <i class="ti ti-layout-dashboard"></i>
              <span class="hide-menu">Accueil</span>
            </a>
          </li>

          <li class="sidebar-item">
            <a class="sidebar-link" (click)="navigateToReportManagement()" aria-expanded="false">
              <i class="ti ti-file-text"></i>
              <span class="hide-menu">Gestion des Rapports</span>
            </a>
          </li>

          <li class="sidebar-item">
            <a class="sidebar-link" (click)="navigateToProfile()" aria-expanded="false">
              <i class="ti ti-user"></i>
              <span class="hide-menu">Mon Profil</span>
            </a>
          </li>

          <li class="sidebar-item">
            <a class="sidebar-link" (click)="logout()" aria-expanded="false">
              <i class="ti ti-logout"></i>
              <span class="hide-menu">Déconnexion</span>
            </a>
          </li>
        </ul>
      </nav>
      <!-- End Sidebar navigation -->
    </div>
    <!-- End Sidebar scroll-->
  </aside>
  <!--  Sidebar End -->

  <!--  Main wrapper -->
  <div class="body-wrapper">
    <!--  Header Start -->
    <header class="app-header">
      <nav class="navbar navbar-expand-lg navbar-light">
        <ul class="navbar-nav">
          <li class="nav-item d-block d-xl-none">
            <a class="nav-link sidebartoggler nav-icon-hover" id="headerCollapse" href="javascript:void(0)">
              <i class="ti ti-menu-2"></i>
            </a>
          </li>
        </ul>
        <div class="navbar-collapse justify-content-end px-0" id="navbarNav">
          <ul class="navbar-nav flex-row ms-auto align-items-center justify-content-end">
            <li class="nav-item dropdown">
              <a class="nav-link nav-icon-hover" href="javascript:void(0)" id="drop2" data-bs-toggle="dropdown"
                aria-expanded="false">
                <img src="../assets/images/profile/user-1.jpg" alt="" width="35" height="35" class="rounded-circle">
              </a>
              <div class="dropdown-menu dropdown-menu-end dropdown-menu-animate-up" aria-labelledby="drop2">
                <div class="message-body">
                  <a href="javascript:void(0)" class="d-flex align-items-center gap-2 dropdown-item">
                    <i class="ti ti-user fs-6"></i>
                    <p class="mb-0 fs-3">{{currentUser?.username}}</p>
                  </a>
                  <a href="javascript:void(0)" (click)="logout()" class="btn btn-outline-primary mx-3 mt-2 d-block">Déconnexion</a>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </nav>
    </header>
    <!--  Header End -->

    <div class="container-fluid">
      <div class="welcome-header">
        <h1>Bienvenue {{currentUser?.username}}</h1>
        <p>Tableau de bord Rapporteur - Gérez vos rapports de classe</p>
      </div>

      <!-- Dashboard Cards -->
      <div class="row">
        <div class="col-lg-6 col-md-6">
          <div class="card" (click)="navigateToReportManagement()" style="cursor: pointer;">
            <div class="card-body">
              <div class="row align-items-start">
                <div class="col-8">
                  <h5 class="card-title mb-9 fw-semibold">Rapports</h5>
                  <h4 class="fw-semibold mb-3">Gestion des rapports</h4>
                  <div class="d-flex align-items-center pb-1">
                    <span class="me-2 rounded-circle bg-light-primary round-20 d-flex align-items-center justify-content-center">
                      <i class="ti ti-arrow-up-right text-primary"></i>
                    </span>
                    <p class="text-dark me-1 fs-3 mb-0">Créer et gérer</p>
                  </div>
                </div>
                <div class="col-4">
                  <div class="d-flex justify-content-end">
                    <div class="text-white bg-primary rounded-circle p-6 d-flex align-items-center justify-content-center">
                      <i class="ti ti-file-text fs-6"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
