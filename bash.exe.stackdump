Stack trace:
Frame         Function      Args
0007FFFFBBA0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFBBA0, 0007FFFFAAA0) msys-2.0.dll+0x1FEBA
0007FFFFBBA0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE78) msys-2.0.dll+0x67F9
0007FFFFBBA0  000210046832 (000210285FF9, 0007FFFFBA58, 0007FFFFBBA0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBA0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBBA0  0002100690B4 (0007FFFFBBB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBE80  00021006A49D (0007FFFFBBB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCC2130000 ntdll.dll
7FFCC0510000 KERNEL32.DLL
7FFCBF8F0000 KERNELBASE.dll
7FFCC05E0000 USER32.dll
7FFCC0120000 win32u.dll
7FFCC0410000 GDI32.dll
7FFCBFDF0000 gdi32full.dll
7FFCBF850000 msvcp_win.dll
7FFCBFBF0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCC1C00000 advapi32.dll
7FFCC0860000 msvcrt.dll
7FFCC09C0000 sechost.dll
7FFCC17F0000 RPCRT4.dll
7FFCBFDC0000 bcrypt.dll
7FFCBF140000 CRYPTBASE.DLL
7FFCBF7C0000 bcryptPrimitives.dll
7FFCC0270000 IMM32.DLL
