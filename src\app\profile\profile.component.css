.welcome-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 10px;
  margin-bottom: 2rem;
  text-align: center;
}

.welcome-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.welcome-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 0;
}

.card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  border-bottom: none;
  padding: 1.5rem;
}

.card-header h5 {
  font-weight: 600;
  display: flex;
  align-items: center;
}

.card-body {
  padding: 2rem;
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.form-label i {
  color: #6c757d;
}

.form-control, .form-select {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-control[readonly] {
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

.form-select[disabled] {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  opacity: 0.8;
}

.btn {
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
}

.btn-success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  border: none;
}

.btn-success:hover {
  background: linear-gradient(135deg, #4e9a2a 0%, #96d4b8 100%);
  transform: translateY(-2px);
}

.btn-secondary {
  background: #6c757d;
  border: none;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.alert {
  border: none;
  border-radius: 10px;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
}

.alert-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

.text-muted {
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.text-danger.small {
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-header h1 {
    font-size: 2rem;
  }
  
  .welcome-header p {
    font-size: 1rem;
  }
  
  .card-body {
    padding: 1.5rem;
  }
  
  .btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
}

/* Animation for form transitions */
.form-control, .form-select {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom styling for disabled state */
.form-control[readonly]:focus,
.form-select[disabled]:focus {
  border-color: #dee2e6;
  box-shadow: none;
}

/* Profile image placeholder */
.rounded-circle {
  border: 2px solid #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Header styling */
.app-header {
  background: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.left-sidebar {
  background: #fff;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.login-header img {
  filter: drop-shadow(0 2px 10px rgba(0, 0, 0, 0.1));
}
